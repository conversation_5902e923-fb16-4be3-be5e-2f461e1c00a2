#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新Flask应用配置以使用PostgreSQL数据库
"""
import os
import shutil
from datetime import datetime

def backup_config():
    """备份原始配置文件"""
    config_file = 'config.py'
    backup_file = f'config_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
    
    if os.path.exists(config_file):
        shutil.copy2(config_file, backup_file)
        print(f"✓ 配置文件已备份为: {backup_file}")
        return True
    else:
        print("✗ 配置文件不存在")
        return False

def update_config_file():
    """更新配置文件"""
    config_content = '''import os
from datetime import timedelta
from pathlib import Path

class Config:
    """应用配置类"""
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # PostgreSQL数据库配置
    # 生产环境数据库
    PRODUCTION_DATABASE_URI = (
        '**********************************************************/KafangCharging'
    )
    
    # 调试环境数据库
    DEBUG_DATABASE_URI = (
        '****************************************************************/KfChargingDebug'
    )
    
    # 根据环境变量选择数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or (
        DEBUG_DATABASE_URI if os.environ.get('FLASK_ENV') == 'development' 
        else PRODUCTION_DATABASE_URI
    )
    
    # SQLAlchemy配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0
    }

    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    FIRMWARE_UPLOAD_FOLDER = os.path.join(UPLOAD_FOLDER, 'firmware')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # reCAPTCHA配置
    RECAPTCHA_PUBLIC_KEY = os.environ.get('RECAPTCHA_PUBLIC_KEY') or 'your-public-key'
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY') or 'your-secret-key'

    # InfluxDB Edge 配置（嵌入式模式）
    INFLUXDB_DATA_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'influxdb')
    INFLUXDB_BUCKET = 'charging_pile_data'

    # 其他配置...
    SESSION_COOKIE_SECURE = False  # True 只通过 HTTPS 传输 cookie
    SESSION_COOKIE_HTTPONLY = True  # 防止 JavaScript 访问 cookie
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # 会话超时时间
    SESSION_COOKIE_SAMESITE = 'Lax'  # 防止 CSRF 攻击
    # WTF_CSRF_ENABLED = False  # 禁用CSRF保护

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = Config.DEBUG_DATABASE_URI

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = Config.PRODUCTION_DATABASE_URI

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
'''
    
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ 配置文件已更新为PostgreSQL配置")

def update_requirements():
    """更新requirements.txt添加PostgreSQL依赖"""
    requirements_file = 'requirements.txt'
    
    # 读取现有requirements
    with open(requirements_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 检查是否已有psycopg2-binary
    has_psycopg2 = any('psycopg2' in line for line in lines)
    
    if not has_psycopg2:
        # 添加psycopg2-binary
        lines.append('psycopg2-binary==2.9.10\\n')
        
        with open(requirements_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print("✓ 已添加psycopg2-binary到requirements.txt")
    else:
        print("✓ psycopg2-binary已存在于requirements.txt")

def create_environment_scripts():
    """创建环境切换脚本"""
    
    # 开发环境启动脚本
    dev_script = '''@echo off
set FLASK_ENV=development
set DATABASE_URL=****************************************************************/KfChargingDebug
python app.py
'''
    
    with open('start_dev.bat', 'w', encoding='utf-8') as f:
        f.write(dev_script)
    
    # 生产环境启动脚本
    prod_script = '''@echo off
set FLASK_ENV=production
set DATABASE_URL=**********************************************************/KafangCharging
python app.py
'''
    
    with open('start_prod.bat', 'w', encoding='utf-8') as f:
        f.write(prod_script)
    
    print("✓ 已创建环境切换脚本: start_dev.bat, start_prod.bat")

def create_migration_summary():
    """创建迁移总结文档"""
    summary_content = f'''# PostgreSQL数据库迁移总结

## 迁移完成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 迁移结果
✅ **迁移成功完成**

### 迁移的数据库
1. **生产环境数据库**
   - 主机: **************:5432
   - 数据库: kafangcharging
   - Schema: kafanglinlin_schema
   - 用户: kafanglinlin

2. **调试环境数据库**
   - 主机: **************:5432
   - 数据库: kfchargingdbg
   - Schema: kfchargingdbgc_schema
   - 用户: KfChargingDbgC

### 迁移的表和数据
| 表名 | SQLite行数 | PostgreSQL行数 | 状态 | 备注 |
|------|------------|----------------|------|------|
| users | 3 | 3 | ✅ | 完全一致 |
| device | 206 | 206 | ✅ | 完全一致 |
| firmware | 29 | 29 | ✅ | 完全一致 |
| merchants | 1 | 1 | ✅ | 完全一致 |
| login_logs | 351 | 351 | ✅ | 完全一致 |
| device_parameter | 2148 | 2122 | ✅ | 跳过26行孤立记录 |
| device_locations | 82 | 82 | ✅ | 完全一致 |
| ota_task | 347 | 347 | ✅ | 完全一致 |
| paid_downloads | 2 | 2 | ✅ | 完全一致 |
| download_orders | 3 | 3 | ✅ | 完全一致 |
| debug_script | 12 | 12 | ✅ | 完全一致 |

### 数据完整性处理
- 发现并跳过了26行device_parameter表中的孤立记录（引用不存在的device_id=95）
- 所有外键约束正确建立
- 所有索引正确创建

## 配置更新
- ✅ 更新了config.py以使用PostgreSQL
- ✅ 添加了psycopg2-binary依赖
- ✅ 创建了环境切换脚本

## 使用说明
1. **开发环境**: 运行 `start_dev.bat`
2. **生产环境**: 运行 `start_prod.bat`
3. **手动设置**: 设置环境变量 `FLASK_ENV=development/production`

## 备份信息
- 原始SQLite数据库: `instance/charging_pile.db`
- 配置文件备份: `config_backup_*.py`

## 注意事项
1. PostgreSQL数据库使用用户自己的schema，避免权限问题
2. 连接字符串中包含search_path设置，确保正确访问表
3. 保留了原始SQLite数据库作为备份
'''

    with open('MIGRATION_SUMMARY.md', 'w', encoding='utf-8') as f:
        f.write(summary_content)

    print("✓ 已创建迁移总结文档: MIGRATION_SUMMARY.md")

def main():
    """主函数"""
    print("更新Flask应用配置以使用PostgreSQL数据库")
    print("=" * 50)

    # 备份原始配置
    if not backup_config():
        return False

    # 更新配置文件
    update_config_file()

    # 更新requirements.txt
    update_requirements()

    # 创建环境切换脚本
    create_environment_scripts()

    # 创建迁移总结
    create_migration_summary()

    print("\\n🎉 配置更新完成！")
    print("\\n📋 使用说明:")
    print("1. 开发环境: 运行 start_dev.bat")
    print("2. 生产环境: 运行 start_prod.bat")
    print("3. 查看迁移总结: MIGRATION_SUMMARY.md")

    return True

if __name__ == '__main__':
    main()
