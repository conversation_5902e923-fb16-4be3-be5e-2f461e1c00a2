#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证数据库迁移是否成功
"""
import psycopg2
import sqlite3
import os

# 数据库配置
SQLITE_DB_PATH = r'C:\Users\<USER>\Desktop\WorkPlace\Charging_Pile\web_admin\instance\charging_pile.db'

POSTGRESQL_CONFIGS = {
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa',
        'schema': 'kafanglinlin_schema'
    },
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz',
        'schema': 'kfchargingdbgc_schema'
    }
}

def get_sqlite_counts():
    """获取SQLite数据库中各表的行数"""
    if not os.path.exists(SQLITE_DB_PATH):
        print(f'SQLite数据库文件不存在: {SQLITE_DB_PATH}')
        return {}
    
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'alembic_version'")
        tables = [row[0] for row in cursor.fetchall()]
        
        counts = {}
        for table_name in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            counts[table_name] = cursor.fetchone()[0]
        
        conn.close()
        return counts
        
    except Exception as e:
        print(f'获取SQLite数据失败: {e}')
        return {}

def get_postgresql_counts(config):
    """获取PostgreSQL数据库中各表的行数"""
    try:
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            database=config['database'],
            user=config['user'],
            password=config['password'],
            sslmode='disable'
        )
        cursor = conn.cursor()
        
        schema_name = config['schema']
        
        # 获取schema中的所有表
        cursor.execute(f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = '{schema_name}'
            ORDER BY table_name
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        counts = {}
        for table_name in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {schema_name}.{table_name}")
            counts[table_name] = cursor.fetchone()[0]
        
        conn.close()
        return counts
        
    except Exception as e:
        print(f'获取PostgreSQL数据失败: {e}')
        return {}

def verify_migration():
    """验证迁移结果"""
    print("数据库迁移验证")
    print("=" * 60)
    
    # 获取SQLite数据
    print("获取SQLite数据...")
    sqlite_counts = get_sqlite_counts()
    if not sqlite_counts:
        print("无法获取SQLite数据，验证终止")
        return False
    
    print(f"SQLite数据库包含 {len(sqlite_counts)} 个表")
    
    # 预期的数据差异（由于跳过孤立记录）
    expected_differences = {
        'device_parameter': 26  # 跳过26行孤立记录
    }
    
    success_count = 0
    total_count = len(POSTGRESQL_CONFIGS)
    
    for db_name, config in POSTGRESQL_CONFIGS.items():
        print(f"\n验证 {db_name} 数据库...")
        print("-" * 40)
        
        pg_counts = get_postgresql_counts(config)
        if not pg_counts:
            print(f"无法获取 {db_name} 数据库数据")
            continue
        
        print(f"PostgreSQL数据库包含 {len(pg_counts)} 个表")
        
        # 比较数据
        all_match = True
        for table_name in sqlite_counts:
            sqlite_count = sqlite_counts[table_name]
            pg_count = pg_counts.get(table_name, 0)
            
            expected_diff = expected_differences.get(table_name, 0)
            expected_pg_count = sqlite_count - expected_diff
            
            if pg_count == expected_pg_count:
                if expected_diff > 0:
                    print(f"  {table_name}: SQLite {sqlite_count}, PostgreSQL {pg_count} (跳过 {expected_diff} 行) ✓")
                else:
                    print(f"  {table_name}: {sqlite_count} 行 ✓")
            else:
                print(f"  {table_name}: SQLite {sqlite_count}, PostgreSQL {pg_count}, 预期 {expected_pg_count} ✗")
                all_match = False
        
        if all_match:
            print(f"\n✓ {db_name} 数据库迁移成功！")
            success_count += 1
        else:
            print(f"\n✗ {db_name} 数据库迁移存在问题")
    
    print(f"\n{'='*60}")
    print(f"验证结果: {success_count}/{total_count} 个数据库迁移成功")
    
    if success_count == total_count:
        print("🎉 所有数据库迁移成功！")
        return True
    else:
        print("❌ 部分数据库迁移失败")
        return False

if __name__ == '__main__':
    success = verify_migration()
    if success:
        print("\n✅ 数据库迁移验证通过，可以继续下一步配置更新")
    else:
        print("\n❌ 数据库迁移验证失败，请检查问题")
