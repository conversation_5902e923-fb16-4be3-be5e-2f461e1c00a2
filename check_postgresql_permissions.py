#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查PostgreSQL数据库权限和schema
"""
import psycopg2

# 数据库连接配置
DATABASES = {
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa'
    },
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz'
    }
}

def check_permissions(db_config, db_name):
    """检查数据库权限和schema"""
    try:
        print(f"\n正在检查 {db_name} 数据库权限...")
        
        # 建立连接
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['database'],
            user=db_config['user'],
            password=db_config['password'],
            sslmode='disable'
        )
        
        cursor = conn.cursor()
        
        # 检查当前用户
        cursor.execute("SELECT current_user, current_database();")
        user_info = cursor.fetchone()
        print(f"当前用户: {user_info[0]}, 当前数据库: {user_info[1]}")
        
        # 检查可用的schema
        cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY schema_name;
        """)
        schemas = cursor.fetchall()
        print(f"可用的schema: {[s[0] for s in schemas]}")
        
        # 检查当前用户在各个schema的权限
        for schema in schemas:
            schema_name = schema[0]
            try:
                cursor.execute(f"""
                    SELECT has_schema_privilege(current_user, '{schema_name}', 'CREATE');
                """)
                can_create = cursor.fetchone()[0]
                print(f"  {schema_name}: CREATE权限 = {can_create}")
                
                # 如果有CREATE权限，测试创建表
                if can_create:
                    try:
                        cursor.execute(f"""
                            CREATE TABLE IF NOT EXISTS {schema_name}.test_migration_table (
                                id SERIAL PRIMARY KEY,
                                test_column VARCHAR(50)
                            );
                        """)
                        print(f"    ✓ 可以在 {schema_name} 中创建表")
                        
                        # 清理测试表
                        cursor.execute(f"DROP TABLE IF EXISTS {schema_name}.test_migration_table;")
                        conn.commit()
                        
                    except Exception as e:
                        print(f"    ✗ 无法在 {schema_name} 中创建表: {e}")
                        conn.rollback()
                        
            except Exception as e:
                print(f"  {schema_name}: 权限检查失败 - {e}")
        
        # 检查是否可以创建schema
        try:
            test_schema = f"{db_config['user']}_schema"
            cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {test_schema};")
            print(f"✓ 可以创建schema: {test_schema}")
            
            # 测试在自己的schema中创建表
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {test_schema}.test_table (
                    id SERIAL PRIMARY KEY,
                    test_column VARCHAR(50)
                );
            """)
            print(f"✓ 可以在自己的schema中创建表")
            
            # 清理
            cursor.execute(f"DROP TABLE IF EXISTS {test_schema}.test_table;")
            cursor.execute(f"DROP SCHEMA IF EXISTS {test_schema};")
            conn.commit()
            
        except Exception as e:
            print(f"✗ 无法创建schema: {e}")
            conn.rollback()
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("PostgreSQL数据库权限检查")
    print("=" * 50)
    
    for db_name, db_config in DATABASES.items():
        check_permissions(db_config, db_name)
    
    print("\n检查完成")

if __name__ == '__main__':
    main()
