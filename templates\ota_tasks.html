{% extends "base.html" %}
<!-- stylelint-disable -->

{% block title %}OTA任务 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-sync-alt"></i> OTA任务</h2>
    </div>
</div>

<div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0"><i class="fas fa-list"></i> 任务列表</h5>
                        </div>
                        <div class="col-auto">
                            <div class="row g-3">
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="dateFilter">
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="成功">成功</option>
                                            <option value="失败">失败</option>
                                            <option value="等待中">等待中</option>
                                            <option value="进行中">进行中</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="deviceIdFilter" placeholder="设备ID">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 加载指示器 -->
                    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载任务列表...</div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>设备ID</th>
                                    <th>固件版本</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>创建时间</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                <!-- 任务列表将通过Ajax动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="d-flex justify-content-between align-items-center p-3 border-top">
                        <div class="text-muted">
                            <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
                        </div>
                        <nav aria-label="任务列表分页">
                            <ul class="pagination pagination-sm mb-0" id="paginationControls">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskDetailsModalLabel">
                    <i class="fas fa-info-circle"></i> 任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>任务ID:</strong> <span id="detailTaskId"></span></p>
                        <p><strong>设备ID:</strong> <span id="detailDeviceId"></span></p>
                        <p><strong>固件版本:</strong> <span id="detailFirmwareVersion"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>状态:</strong> <span id="detailStatus"></span></p>
                        <p><strong>进度:</strong> <span id="detailProgress"></span>%</p>
                        <p><strong>创建时间:</strong> <span id="detailCreatedAt"></span></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <p><strong>错误信息:</strong></p>
                        <pre id="detailError" class="bg-light p-2" style="max-height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 通知组件 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="notificationIcon" class="fas fa-info-circle me-2"></i>
            <strong id="notificationTitle" class="me-auto">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="notificationMessage">
            <!-- 通知内容 -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script>
    // 打印调试日志到控制台
    console.log("OTA任务脚本加载中...");

    // 全局变量定义
    let taskDetailsModal = null;
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    let currentPage = 1;
    let currentFilters = {};
    let notificationToast = null;
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 处理筛选条件变化
    function handleFilterChange() {
        currentFilters = {
            search: '', // OTA任务页面暂时不需要通用搜索
            status: document.getElementById('statusFilter').value,
            device_id: document.getElementById('deviceIdFilter').value.trim(),
            firmware: '', // 暂时不需要固件版本筛选
            date: document.getElementById('dateFilter').value
        };

        currentPage = 1; // 重置到第一页
        loadTaskList(currentPage);
    }

    // 添加筛选事件监听器
    function setupEventListeners() {
        document.getElementById('dateFilter').addEventListener('change', handleFilterChange);
        document.getElementById('statusFilter').addEventListener('change', handleFilterChange);
        document.getElementById('deviceIdFilter').addEventListener('keyup', debounce(handleFilterChange, 500));
    }

    // 显示通知
    function showNotification(message, type = 'info', title = '通知') {
        const toast = document.getElementById('notificationToast');
        const icon = document.getElementById('notificationIcon');
        const titleEl = document.getElementById('notificationTitle');
        const messageEl = document.getElementById('notificationMessage');

        // 设置图标和样式
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
            icon.className = 'fas fa-check-circle me-2';
        } else if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
            icon.className = 'fas fa-exclamation-circle me-2';
        } else if (type === 'warning') {
            toast.classList.add('bg-warning', 'text-dark');
            icon.className = 'fas fa-exclamation-triangle me-2';
        } else {
            toast.classList.add('bg-info', 'text-white');
            icon.className = 'fas fa-info-circle me-2';
        }

        titleEl.textContent = title;
        messageEl.textContent = message;

        if (!notificationToast) {
            notificationToast = new bootstrap.Toast(toast);
        }
        notificationToast.show();
    }

    // 显示/隐藏加载状态
    function showLoading(show) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const tableBody = document.getElementById('taskTableBody');

        if (show) {
            loadingIndicator.style.display = 'block';
            tableBody.style.opacity = '0.5';
        } else {
            loadingIndicator.style.display = 'none';
            tableBody.style.opacity = '1';
        }
    }

    // 加载任务列表
    function loadTaskList(page = 1) {
        showLoading(true);

        // 构建查询参数
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            ...currentFilters
        });

        fetch(`/api/ota/tasks?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderTaskList(data.tasks);
                    renderPagination(data.pagination);
                    currentPage = page;
                } else {
                    showNotification('加载任务列表失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('加载任务列表失败:', error);
                showNotification('加载任务列表失败，请重试', 'error');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    // 渲染任务列表
    function renderTaskList(tasks) {
        const tbody = document.getElementById('taskTableBody');
        tbody.innerHTML = '';

        tasks.forEach(task => {
            const row = createTaskRow(task);
            tbody.appendChild(row);
        });
    }

    // 创建任务行
    function createTaskRow(task) {
        const row = document.createElement('tr');
        row.setAttribute('data-task-id', task.id);

        // 状态标签HTML
        let statusHtml = '';
        if (task.status === '成功') {
            statusHtml = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>成功</span>';
        } else if (task.status === '失败') {
            statusHtml = '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>失败</span>';
        } else if (task.status === '等待中') {
            statusHtml = '<span class="badge bg-secondary"><i class="fas fa-hourglass-half me-1"></i>等待中</span>';
        } else {
            statusHtml = '<span class="badge bg-warning"><i class="fas fa-sync fa-spin me-1"></i>进行中</span>';
        }

        // 操作按钮HTML
        let actionButtons = `
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-info" onclick="viewTaskDetails('${task.id}')" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>`;

        if (task.status === '失败') {
            actionButtons += `
                <button type="button" class="btn btn-sm btn-warning" onclick="retryTask('${task.id}')" title="重试">
                    <i class="fas fa-redo"></i>
                </button>`;
        }

        actionButtons += `
                <button type="button" class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>`;

        row.innerHTML = `
            <td>${task.id}</td>
            <td>${task.device_name}</td>
            <td><span class="badge bg-primary">v${task.firmware_version}</span></td>
            <td>${statusHtml}</td>
            <td style="width: 200px;">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped ${task.status === '进行中' ? 'progress-bar-animated' : ''}"
                         role="progressbar"
                         data-progress="${task.progress}"
                         aria-valuenow="${task.progress}"
                         aria-valuemin="0"
                         aria-valuemax="100"
                         style="width: ${task.progress}%">
                        ${task.progress}%
                    </div>
                </div>
            </td>
            <td>${task.created_at}</td>
            <td>${task.updated_at}</td>
            <td>${actionButtons}</td>
        `;

        return row;
    }

    // 渲染分页控件
    function renderPagination(pagination) {
        const paginationInfo = document.getElementById('paginationInfo');
        const paginationControls = document.getElementById('paginationControls');

        // 更新分页信息
        const start = (pagination.page - 1) * pagination.per_page + 1;
        const end = Math.min(pagination.page * pagination.per_page, pagination.total);
        paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${pagination.total} 条记录`;

        // 生成分页按钮
        paginationControls.innerHTML = '';

        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.prev_num || 1})">上一页</a>`;
        paginationControls.appendChild(prevLi);

        // 页码按钮
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${i})">${i}</a>`;
            paginationControls.appendChild(li);
        }

        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.next_num || pagination.pages})">下一页</a>`;
        paginationControls.appendChild(nextLi);
    }

    // WebSocket连接初始化函数
    function initializeWebSocket() {
        try {
            console.log("初始化WebSocket连接...");
            
            // 创建Socket.IO连接
            socket = io(window.location.origin, {
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                reconnectionAttempts: maxReconnectAttempts
            });
            

            // 连接成功事件
            socket.on('connect', function() {
                console.log('WebSocket连接成功，Socket ID:', socket.id);
                reconnectAttempts = 0;
                console.log('注册的事件监听器:', socket._callbacks);
            });
            
            // 连接错误事件
            socket.on('connect_error', function(error) {
                console.error('WebSocket连接错误:', error);
                reconnectAttempts++;
                
                if (reconnectAttempts >= maxReconnectAttempts) {
                    console.error('WebSocket重连次数超过限制，请刷新页面重试');
                }
            });

            socket.on('disconnect', function(reason) {
                console.log('WebSocket断开连接，原因:', reason);
            });
            
            // 监听任务状态更新
            socket.on('ota_task_update', function(data) {
                console.log("收到任务状态更新:", data);
                if (data && data.task_id) {
                    updateTaskStatus(data);
                } else {
                    console.error("收到的数据格式不正确:", data);
                }
            });

            socket.onAny((eventName, ...args) => {
                console.log('收到事件:', eventName, args);
            });
        } catch (error) {
            console.error('初始化WebSocket失败:', error);
        }
    }
    
    // 更新任务状态的函数
    function updateTaskStatus(data) {
        const taskRow = document.querySelector(`tr[data-task-id="${data.task_id}"]`);
        if (taskRow) {
            console.log("找到任务行，更新状态");
            // 更新状态
            const statusCell = taskRow.querySelector('td:nth-child(4)');
            const progressBar = taskRow.querySelector('.progress-bar');
            
            // 更新状态标签
            let statusHtml = '';
            if (data.status === '成功') {
                statusHtml = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>成功</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
                
                console.log("任务成功");
            } else if (data.status === '失败') {
                statusHtml = '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>失败</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-danger');
                
                console.log("任务失败，添加重试按钮");
                // 为失败的任务添加重试按钮
                const actionCell = taskRow.querySelector('td:nth-child(8)');
                if (actionCell && !actionCell.querySelector('.btn-warning')) {
                    const btnGroup = actionCell.querySelector('.btn-group');
                    if (btnGroup) {
                        const retryBtn = document.createElement('button');
                        retryBtn.type = 'button';
                        retryBtn.className = 'btn btn-sm btn-warning';
                        retryBtn.title = '重试';
                        retryBtn.innerHTML = '<i class="fas fa-redo"></i>';
                        retryBtn.onclick = function() { retryTask(data.task_id); };
                        
                        // 插入到第二个位置
                        if (btnGroup.children.length > 1) {
                            btnGroup.insertBefore(retryBtn, btnGroup.children[1]);
                        } else {
                            btnGroup.appendChild(retryBtn);
                        }
                    }
                }
            } else if (data.status === '等待中') {
                statusHtml = '<span class="badge bg-secondary"><i class="fas fa-hourglass-half me-1"></i>等待中</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-secondary');
            } else {
                // 如果已经添加了动画效果，则不添加
                statusHtml = '<span class="badge bg-warning"><i class="fas fa-sync fa-spin me-1"></i>进行中</span>';
                progressBar.classList.add('progress-bar-animated', 'progress-bar-striped');
                // if (!progressBar.classList.contains('progress-bar-animated')) {
                // }
                console.log("任务进行中");
            }
            statusCell.innerHTML = statusHtml;
            
            // 更新进度条 - 添加平滑过渡效果
            const currentProgress = parseInt(progressBar.getAttribute('aria-valuenow') || 0);
            const targetProgress = data.progress;
            
            // 如果进度有变化，添加过渡效果
            if (currentProgress !== targetProgress) {
                // 添加过渡效果
                progressBar.style.transition = 'width 0.5s ease-in-out';
                
                // 设置新的进度值
                progressBar.style.width = `${targetProgress}%`;
                progressBar.setAttribute('aria-valuenow', targetProgress);
                progressBar.textContent = `${targetProgress}%`;
                
                // 记录进度变化
                console.log(`进度从 ${currentProgress}% 更新到 ${targetProgress}%`);
            }
            
            // 如果任务完成，显示通知
            if (data.status === '成功' || data.status === '失败') {
                console.log("任务完成，显示通知");
                const message = data.status === '成功' ? '任务执行成功' : '任务执行失败';
                const type = data.status === '成功' ? 'success' : 'error';
                showNotification(message, type);
            }
        } else {
            console.log("未找到任务行");
        }
    }
    
    // 确保DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM已加载完成，开始初始化...");

        // 初始化通知组件
        const notificationToastEl = document.getElementById('notificationToast');
        if (notificationToastEl) {
            notificationToast = new bootstrap.Toast(notificationToastEl);
        }

        // 初始化模态框
        const taskDetailsModalEl = document.getElementById('taskDetailsModal');
        if (taskDetailsModalEl) {
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
            console.log("模态框初始化成功");
        } else {
            console.error("无法找到模态框元素");
        }

        // 设置事件监听器
        setupEventListeners();

        // 初始化WebSocket连接
        initializeWebSocket();

        // 加载任务列表
        loadTaskList(1);
    });

    // 检查是否需要重定向到登录页面
    function checkLoginRedirect(response) {
        // 如果返回了HTML而不是JSON，那么可能是登录页面
        if (response.headers.get('Content-Type').includes('text/html')) {
            console.log("收到HTML响应，可能是登录页面");
            alert("您的登录已过期，将跳转到登录页面");
            window.location.href = '/login';
            return true;
        }
        return false;
    }

    // 查看任务详情
    window.viewTaskDetails = function(taskId) {
        console.log("查看任务详情被点击，任务ID:", taskId);
        
        // 如果模态框还没初始化，则初始化它
        if (!taskDetailsModal) {
            const taskDetailsModalEl = document.getElementById('taskDetailsModal');
            if (!taskDetailsModalEl) {
                console.error("无法找到任务详情模态框元素");
                return;
            }
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
        }
        
        // 显示加载状态
        document.getElementById('detailTaskId').textContent = '加载中...';
        document.getElementById('detailDeviceId').textContent = '加载中...';
        document.getElementById('detailFirmwareVersion').textContent = '加载中...';
        document.getElementById('detailStatus').textContent = '加载中...';
        document.getElementById('detailProgress').textContent = '0';
        document.getElementById('detailCreatedAt').textContent = '加载中...';
        document.getElementById('detailError').textContent = '加载中...';
        
        // 显示模态框
        taskDetailsModal.show();
        
        // 构建正确的URL
        const url = `/ota/task/${taskId}`;
        console.log("正在发送请求到:", url);
        
        // 获取任务详情数据
        fetch(url)
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));
                
                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("获取到的任务数据:", data);
                document.getElementById('detailTaskId').textContent = data.id;
                document.getElementById('detailDeviceId').textContent = data.device_name;
                document.getElementById('detailFirmwareVersion').textContent = data.firmware_version;
                document.getElementById('detailStatus').textContent = data.status;
                document.getElementById('detailProgress').textContent = data.progress;
                document.getElementById('detailCreatedAt').textContent = data.created_at;
                document.getElementById('detailError').textContent = data.error_message || '无';
            })
            .catch(error => {
                console.error('获取任务详情失败:', error);
                if (error.message !== '需要登录') {
                    alert('获取任务详情失败: ' + error.message);
                    // 关闭模态框
                    taskDetailsModal.hide();
                }
            });
    };
    
    // 重试任务
    window.retryTask = function(taskId) {
        console.log("重试任务被点击，任务ID:", taskId);
        
        if (confirm('确定要重试此任务吗？')) {
            // 构建正确的URL
            const url = `/ota/task/${taskId}/retry`;
            console.log("正在发送POST请求到:", url);
            
            // 设置请求头
            const headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            };
            
            fetch(url, {
                method: 'POST',
                headers: headers,
                credentials: 'same-origin'
            })
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));
                
                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("重试任务响应:", data);
                if (data.success) {
                    showNotification('任务已重新开始', 'success');
                    // 刷新当前页面的任务列表
                    loadTaskList(currentPage);
                } else {
                    showNotification(data.message || '重试失败', 'error');
                }
            })
            .catch(error => {
                console.error('重试任务失败:', error);
                if (error.message !== '需要登录') {
                    alert('重试任务失败: ' + error.message);
                }
            });
        }
    };

    // 删除任务
    window.deleteTask = function(taskId) {
        console.log("删除任务被点击，任务ID:", taskId);

        if (confirm('确定要删除此任务吗？')) {
            const url = `/api/ota/task/delete/${taskId}`;
            console.log("正在发送DELETE请求到:", url);

            fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));

                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }

                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("删除任务响应:", data);
                if (data.success) {
                    showNotification('任务删除成功', 'success');
                    if (data.warning) {
                        showNotification(data.warning, 'warning');
                    }
                    // 刷新当前页面的任务列表
                    loadTaskList(currentPage);
                } else {
                    showNotification(data.message || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('删除任务失败:', error);
                if (error.message !== '需要登录') {
                    showNotification('删除任务失败: ' + error.message, 'error');
                }
            });
        }
    };

    console.log("OTA任务脚本加载完成");
</script>
{% endblock %} 