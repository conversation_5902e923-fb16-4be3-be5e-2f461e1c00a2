#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SQLite到PostgreSQL数据库迁移脚本
"""
import sqlite3
import psycopg2
import os
import sys
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# SQLite数据库路径
SQLITE_DB_PATH = r'C:\Users\<USER>\Desktop\WorkPlace\Charging_Pile\web_admin\instance\charging_pile.db'

# PostgreSQL数据库配置
POSTGRESQL_CONFIGS = {
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'kafangcharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa'
    },
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'kfchargingdbg',
        'user': 'KfChargingDbgC',
        'password': 'JT5WJ6Zn3hbAcWBz'
    }
}

# 表结构映射（SQLite -> PostgreSQL）
TABLE_SCHEMAS = {
    'users': """
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE,
            password_hash VARCHAR(256),
            is_admin BOOLEAN DEFAULT FALSE,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'device': """
        CREATE TABLE IF NOT EXISTS device (
            id SERIAL PRIMARY KEY,
            device_id VARCHAR(80) UNIQUE NOT NULL,
            device_remark VARCHAR(200),
            product_key VARCHAR(80) NOT NULL,
            firmware_version VARCHAR(20) DEFAULT '未知',
            last_ota_time TIMESTAMP,
            last_ota_status VARCHAR(20) DEFAULT '未升级',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'firmware': """
        CREATE TABLE IF NOT EXISTS firmware (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            version VARCHAR(20) NOT NULL,
            file_path VARCHAR(255) NOT NULL,
            size INTEGER NOT NULL,
            crc32 VARCHAR(8) NOT NULL DEFAULT '00000000',
            description TEXT,
            upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            download_count INTEGER DEFAULT 0
        );
    """,
    'login_logs': """
        CREATE TABLE IF NOT EXISTS login_logs (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            username VARCHAR(80) NOT NULL,
            ip_address VARCHAR(50),
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            result VARCHAR(20)
        );
    """,
    'device_parameter': """
        CREATE TABLE IF NOT EXISTS device_parameter (
            id SERIAL PRIMARY KEY,
            device_id INTEGER REFERENCES device(id) NOT NULL,
            param_name VARCHAR(50) NOT NULL,
            param_value VARCHAR(100),
            description VARCHAR(200),
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'device_locations': """
        CREATE TABLE IF NOT EXISTS device_locations (
            id SERIAL PRIMARY KEY,
            device_id VARCHAR(50) REFERENCES device(device_id) NOT NULL,
            latitude FLOAT NOT NULL,
            longitude FLOAT NOT NULL,
            address VARCHAR(200),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'ota_task': """
        CREATE TABLE IF NOT EXISTS ota_task (
            id SERIAL PRIMARY KEY,
            device_id INTEGER REFERENCES device(id) NOT NULL,
            firmware_path VARCHAR(255) NOT NULL,
            firmware_version VARCHAR(20) NOT NULL,
            status VARCHAR(20) DEFAULT '等待中',
            progress INTEGER DEFAULT 0,
            error_message TEXT DEFAULT '',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'paid_downloads': """
        CREATE TABLE IF NOT EXISTS paid_downloads (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            file_path VARCHAR(255) NOT NULL,
            file_size INTEGER,
            price NUMERIC(10, 2) NOT NULL,
            download_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER REFERENCES users(id),
            is_active BOOLEAN DEFAULT TRUE
        );
    """,
    'download_orders': """
        CREATE TABLE IF NOT EXISTS download_orders (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id) NOT NULL,
            download_id INTEGER REFERENCES paid_downloads(id) NOT NULL,
            order_no VARCHAR(32) UNIQUE NOT NULL,
            amount NUMERIC(10, 2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            payment_time TIMESTAMP,
            payment_method VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'merchants': """
        CREATE TABLE IF NOT EXISTS merchants (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            contact_person VARCHAR(50) NOT NULL,
            contact_phone VARCHAR(20) NOT NULL,
            contact_email VARCHAR(100),
            address VARCHAR(200),
            business_license VARCHAR(100),
            tax_number VARCHAR(100),
            bank_name VARCHAR(100),
            bank_account VARCHAR(50),
            bank_account_name VARCHAR(100),
            status VARCHAR(20) DEFAULT '正常',
            remark TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    'debug_script': """
        CREATE TABLE IF NOT EXISTS debug_script (
            id SERIAL PRIMARY KEY,
            device_id INTEGER REFERENCES device(id) NOT NULL,
            enabled BOOLEAN DEFAULT FALSE,
            frequency INTEGER DEFAULT 60,
            total_executions INTEGER DEFAULT 0,
            successful_executions INTEGER DEFAULT 0,
            last_execution_time TIMESTAMP,
            last_execution_status VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """
}

# 需要创建的索引
INDEXES = [
    "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);",
    "CREATE INDEX IF NOT EXISTS idx_device_device_id ON device(device_id);",
    "CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_login_logs_login_time ON login_logs(login_time);",
    "CREATE INDEX IF NOT EXISTS idx_device_parameter_device_id ON device_parameter(device_id);",
    "CREATE INDEX IF NOT EXISTS idx_device_locations_device_id ON device_locations(device_id);",
    "CREATE INDEX IF NOT EXISTS idx_ota_task_device_id ON ota_task(device_id);",
    "CREATE INDEX IF NOT EXISTS idx_ota_task_status ON ota_task(status);",
    "CREATE INDEX IF NOT EXISTS idx_download_orders_user_id ON download_orders(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_download_orders_download_id ON download_orders(download_id);",
    "CREATE INDEX IF NOT EXISTS idx_debug_script_device_id ON debug_script(device_id);"
]

class DatabaseMigrator:
    def __init__(self):
        self.sqlite_conn = None
        self.pg_conn = None
        self.pg_cursor = None
        self.schema_name = None
        
    def connect_sqlite(self):
        """连接SQLite数据库"""
        try:
            if not os.path.exists(SQLITE_DB_PATH):
                raise FileNotFoundError(f"SQLite数据库文件不存在: {SQLITE_DB_PATH}")
            
            self.sqlite_conn = sqlite3.connect(SQLITE_DB_PATH)
            logger.info("SQLite数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"连接SQLite数据库失败: {e}")
            return False
    
    def connect_postgresql(self, config):
        """连接PostgreSQL数据库"""
        try:
            self.pg_conn = psycopg2.connect(
                host=config['host'],
                port=config['port'],
                database=config['database'],
                user=config['user'],
                password=config['password'],
                sslmode='disable'
            )
            self.pg_cursor = self.pg_conn.cursor()

            # 设置schema名称为用户名_schema
            self.schema_name = f"{config['user']}_schema"

            # 创建用户schema
            self.pg_cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name};")

            # 设置search_path以使用用户schema
            self.pg_cursor.execute(f"SET search_path TO {self.schema_name}, public;")
            self.pg_conn.commit()

            logger.info(f"PostgreSQL数据库连接成功: {config['database']}, 使用schema: {self.schema_name}")
            return True
        except Exception as e:
            logger.error(f"连接PostgreSQL数据库失败: {e}")
            return False
    
    def clean_existing_data(self):
        """清空现有数据"""
        try:
            logger.info("清空现有数据...")

            # 按依赖关系逆序删除数据
            tables_reverse_order = [
                'debug_script',     # 依赖 device
                'download_orders',  # 依赖 users, paid_downloads
                'paid_downloads',   # 依赖 users
                'ota_task',        # 依赖 device
                'device_locations', # 依赖 device
                'device_parameter', # 依赖 device
                'login_logs',      # 依赖 users
                'merchants',       # 基础表，无外键依赖
                'firmware',        # 基础表，无外键依赖
                'device',          # 基础表，无外键依赖
                'users'           # 基础表，无外键依赖
            ]

            for table_name in tables_reverse_order:
                try:
                    self.pg_cursor.execute(f"TRUNCATE TABLE {self.schema_name}.{table_name} RESTART IDENTITY CASCADE;")
                    logger.info(f"已清空表: {self.schema_name}.{table_name}")
                except Exception as e:
                    # 如果表不存在，忽略错误
                    if "does not exist" in str(e):
                        logger.info(f"表 {self.schema_name}.{table_name} 不存在，跳过清空")
                    else:
                        logger.warning(f"清空表 {self.schema_name}.{table_name} 失败: {e}")

            self.pg_conn.commit()
            logger.info("数据清空完成")
            return True
        except Exception as e:
            logger.error(f"清空数据失败: {e}")
            self.pg_conn.rollback()
            return False

    def create_tables(self):
        """创建PostgreSQL表结构"""
        try:
            logger.info(f"开始在schema {self.schema_name}中创建表结构...")

            for table_name, schema_sql in TABLE_SCHEMAS.items():
                # 修改SQL，添加schema前缀
                schema_sql = schema_sql.replace(
                    f"CREATE TABLE IF NOT EXISTS {table_name}",
                    f"CREATE TABLE IF NOT EXISTS {self.schema_name}.{table_name}"
                )
                logger.info(f"创建表: {self.schema_name}.{table_name}")
                self.pg_cursor.execute(schema_sql)

            # 创建索引，添加schema前缀
            logger.info("创建索引...")
            for index_sql in INDEXES:
                # 替换表名添加schema前缀
                for table_name in TABLE_SCHEMAS.keys():
                    if f"ON {table_name}" in index_sql:
                        index_sql = index_sql.replace(
                            f"ON {table_name}",
                            f"ON {self.schema_name}.{table_name}"
                        )
                self.pg_cursor.execute(index_sql)

            self.pg_conn.commit()
            logger.info(f"在schema {self.schema_name}中的表结构创建完成")
            return True
        except Exception as e:
            logger.error(f"创建表结构失败: {e}")
            self.pg_conn.rollback()
            return False

    def convert_data_types(self, table_name, row, columns):
        """转换数据类型以适配PostgreSQL"""
        converted_row = list(row)

        # 针对不同表的特殊处理
        if table_name == 'users':
            # 转换布尔字段
            for i, col_name in enumerate(columns):
                if col_name in ['is_admin', 'active']:
                    if converted_row[i] is not None:
                        converted_row[i] = bool(converted_row[i])

        elif table_name == 'debug_script':
            # 转换布尔字段
            for i, col_name in enumerate(columns):
                if col_name == 'enabled':
                    if converted_row[i] is not None:
                        converted_row[i] = bool(converted_row[i])

        elif table_name == 'paid_downloads':
            # 转换布尔字段
            for i, col_name in enumerate(columns):
                if col_name == 'is_active':
                    if converted_row[i] is not None:
                        converted_row[i] = bool(converted_row[i])

        return tuple(converted_row)

    def get_filtered_data(self, table_name):
        """获取过滤后的数据，排除外键约束违反的记录"""
        sqlite_cursor = self.sqlite_conn.cursor()

        if table_name == 'device_parameter':
            # 只获取存在于device表中的device_id的记录
            sql = """
                SELECT dp.* FROM device_parameter dp
                INNER JOIN device d ON dp.device_id = d.id
            """
        elif table_name == 'login_logs':
            # 只获取user_id为NULL或存在于users表中的记录
            sql = """
                SELECT ll.* FROM login_logs ll
                LEFT JOIN users u ON ll.user_id = u.id
                WHERE ll.user_id IS NULL OR u.id IS NOT NULL
            """
        elif table_name == 'ota_task':
            # 只获取存在于device表中的device_id的记录
            sql = """
                SELECT ot.* FROM ota_task ot
                INNER JOIN device d ON ot.device_id = d.id
            """
        elif table_name == 'device_locations':
            # 只获取存在于device表中的device_id的记录
            sql = """
                SELECT dl.* FROM device_locations dl
                INNER JOIN device d ON dl.device_id = d.device_id
            """
        elif table_name == 'download_orders':
            # 只获取存在于users和paid_downloads表中的记录
            sql = """
                SELECT do.* FROM download_orders do
                INNER JOIN users u ON do.user_id = u.id
                INNER JOIN paid_downloads pd ON do.download_id = pd.id
            """
        elif table_name == 'debug_script':
            # 只获取存在于device表中的device_id的记录
            sql = """
                SELECT ds.* FROM debug_script ds
                INNER JOIN device d ON ds.device_id = d.id
            """
        else:
            # 其他表直接获取所有数据
            sql = f"SELECT * FROM {table_name}"

        sqlite_cursor.execute(sql)
        return sqlite_cursor.fetchall()

    def migrate_table_data(self, table_name):
        """迁移单个表的数据"""
        try:
            # 获取过滤后的SQLite表数据
            rows = self.get_filtered_data(table_name)

            if not rows:
                logger.info(f"表 {table_name} 无数据，跳过")
                return True

            # 获取列名
            sqlite_cursor = self.sqlite_conn.cursor()
            sqlite_cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = sqlite_cursor.fetchall()
            columns = [col[1] for col in columns_info]

            # 检查是否有被过滤的记录
            sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = sqlite_cursor.fetchone()[0]
            filtered_count = len(rows)

            if total_count > filtered_count:
                logger.warning(f"表 {table_name}: 总记录 {total_count} 行，过滤后 {filtered_count} 行 (跳过了 {total_count - filtered_count} 行孤立记录)")

            logger.info(f"开始迁移表 {table_name}，共 {filtered_count} 行数据")

            # 构建插入SQL，使用schema前缀
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join(columns)
            insert_sql = f"INSERT INTO {self.schema_name}.{table_name} ({columns_str}) VALUES ({placeholders})"

            # 批量插入数据，进行类型转换
            batch_size = 1000
            for i in range(0, len(rows), batch_size):
                batch = rows[i:i + batch_size]
                # 转换数据类型
                converted_batch = [self.convert_data_types(table_name, row, columns) for row in batch]
                self.pg_cursor.executemany(insert_sql, converted_batch)
                logger.info(f"已迁移 {min(i + batch_size, len(rows))}/{len(rows)} 行")

            self.pg_conn.commit()
            logger.info(f"表 {table_name} 迁移完成")
            return True

        except Exception as e:
            logger.error(f"迁移表 {table_name} 失败: {e}")
            self.pg_conn.rollback()
            return False

    def migrate_all_data(self):
        """迁移所有表数据"""
        # 按依赖关系排序的表列表
        tables_order = [
            'users',           # 基础表，无外键依赖
            'device',          # 基础表，无外键依赖
            'firmware',        # 基础表，无外键依赖
            'merchants',       # 基础表，无外键依赖
            'login_logs',      # 依赖 users
            'device_parameter', # 依赖 device
            'device_locations', # 依赖 device
            'ota_task',        # 依赖 device
            'paid_downloads',  # 依赖 users
            'download_orders', # 依赖 users, paid_downloads
            'debug_script'     # 依赖 device
        ]

        success_count = 0
        for table_name in tables_order:
            if self.migrate_table_data(table_name):
                success_count += 1
            else:
                logger.error(f"表 {table_name} 迁移失败，停止迁移")
                return False

        logger.info(f"数据迁移完成，成功迁移 {success_count} 个表")
        return True

    def verify_data_integrity(self):
        """验证数据完整性"""
        try:
            logger.info("开始验证数据完整性...")

            # 获取SQLite表的行数
            sqlite_cursor = self.sqlite_conn.cursor()
            sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'alembic_version'")
            tables = [row[0] for row in sqlite_cursor.fetchall()]

            verification_results = {}
            expected_differences = {
                'device_parameter': 26  # 预期跳过26行孤立记录
            }

            for table_name in tables:
                # SQLite行数
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                sqlite_count = sqlite_cursor.fetchone()[0]

                # PostgreSQL行数，使用schema前缀
                self.pg_cursor.execute(f"SELECT COUNT(*) FROM {self.schema_name}.{table_name}")
                pg_count = self.pg_cursor.fetchone()[0]

                # 检查是否是预期的差异
                expected_diff = expected_differences.get(table_name, 0)
                expected_pg_count = sqlite_count - expected_diff

                verification_results[table_name] = {
                    'sqlite_count': sqlite_count,
                    'pg_count': pg_count,
                    'expected_pg_count': expected_pg_count,
                    'match': pg_count == expected_pg_count
                }

                if pg_count == expected_pg_count:
                    if expected_diff > 0:
                        logger.info(f"表 {table_name}: SQLite {sqlite_count} 行, PostgreSQL {pg_count} 行 (跳过了 {expected_diff} 行孤立记录，符合预期)")
                    else:
                        logger.info(f"表 {table_name}: {sqlite_count} 行 (一致)")
                else:
                    logger.error(f"表 {table_name}: SQLite {sqlite_count} 行, PostgreSQL {pg_count} 行, 预期 {expected_pg_count} 行 (不一致)")

            # 总结验证结果
            total_tables = len(verification_results)
            matched_tables = sum(1 for result in verification_results.values() if result['match'])

            logger.info(f"数据完整性验证完成: {matched_tables}/{total_tables} 个表数据符合预期")

            return matched_tables == total_tables, verification_results

        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return False, {}

    def close_connections(self):
        """关闭数据库连接"""
        if self.sqlite_conn:
            self.sqlite_conn.close()
        if self.pg_cursor:
            self.pg_cursor.close()
        if self.pg_conn:
            self.pg_conn.close()
        logger.info("数据库连接已关闭")

    def migrate_to_database(self, db_name, config):
        """迁移到指定的PostgreSQL数据库"""
        logger.info(f"开始迁移到 {db_name} 数据库...")

        # 连接数据库
        if not self.connect_postgresql(config):
            return False

        # 创建表结构
        if not self.create_tables():
            return False

        # 清空现有数据
        if not self.clean_existing_data():
            return False

        # 迁移数据
        if not self.migrate_all_data():
            return False

        # 验证数据完整性
        is_valid, results = self.verify_data_integrity()

        return is_valid

def main():
    """主函数"""
    logger.info("开始SQLite到PostgreSQL数据库迁移")
    logger.info("=" * 60)

    migrator = DatabaseMigrator()

    try:
        # 连接SQLite数据库
        if not migrator.connect_sqlite():
            logger.error("无法连接SQLite数据库，迁移终止")
            return False

        success_count = 0
        total_count = len(POSTGRESQL_CONFIGS)

        # 迁移到每个PostgreSQL数据库
        for db_name, config in POSTGRESQL_CONFIGS.items():
            logger.info(f"\n{'='*20} 迁移到 {db_name} 数据库 {'='*20}")

            if migrator.migrate_to_database(db_name, config):
                logger.info(f"✓ {db_name} 数据库迁移成功")
                success_count += 1
            else:
                logger.error(f"✗ {db_name} 数据库迁移失败")

            # 关闭当前PostgreSQL连接
            if migrator.pg_cursor:
                migrator.pg_cursor.close()
            if migrator.pg_conn:
                migrator.pg_conn.close()

        # 总结迁移结果
        logger.info(f"\n{'='*60}")
        logger.info(f"迁移完成: {success_count}/{total_count} 个数据库迁移成功")

        if success_count == total_count:
            logger.info("✓ 所有数据库迁移成功！")
            return True
        else:
            logger.error("✗ 部分数据库迁移失败")
            return False

    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        return False
    finally:
        migrator.close_connections()

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
