#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试PostgreSQL数据库连接
"""
import psycopg2
from psycopg2 import sql

# 数据库连接配置
DATABASES = {
    'production': {
        'host': '**************',
        'port': 5432,
        'database': 'KafangCharging',
        'user': 'kafanglinlin',
        'password': '7jbWNHYZZLMa'
    },
    'debug': {
        'host': '**************',
        'port': 5432,
        'database': 'KfChargingDebug',
        'user': 'kafanglinlin_debug',
        'password': 'ZwJaH8tTkdRM'
    }
}

def test_connection(db_config, db_name):
    """测试数据库连接"""
    try:
        print(f"\n正在测试 {db_name} 数据库连接...")
        
        # 建立连接（允许不安全连接）
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['database'],
            user=db_config['user'],
            password=db_config['password'],
            sslmode='disable'
        )
        
        # 创建游标
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✓ 连接成功！PostgreSQL版本: {version[0]}")
        
        # 检查数据库权限
        cursor.execute("SELECT current_user, current_database();")
        user_info = cursor.fetchone()
        print(f"✓ 当前用户: {user_info[0]}, 当前数据库: {user_info[1]}")
        
        # 检查是否有创建表的权限
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id SERIAL PRIMARY KEY,
                    test_column VARCHAR(50)
                );
            """)
            print("✓ 具有创建表权限")
            
            # 清理测试表
            cursor.execute("DROP TABLE IF EXISTS test_table;")
            conn.commit()
            print("✓ 具有删除表权限")
            
        except Exception as e:
            print(f"✗ 权限测试失败: {e}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("PostgreSQL数据库连接测试")
    print("=" * 50)
    
    success_count = 0
    total_count = len(DATABASES)
    
    for db_name, db_config in DATABASES.items():
        if test_connection(db_config, db_name):
            success_count += 1
    
    print(f"\n测试结果: {success_count}/{total_count} 个数据库连接成功")
    
    if success_count == total_count:
        print("✓ 所有数据库连接正常，可以开始迁移")
        return True
    else:
        print("✗ 部分数据库连接失败，请检查配置")
        return False

if __name__ == '__main__':
    main()
